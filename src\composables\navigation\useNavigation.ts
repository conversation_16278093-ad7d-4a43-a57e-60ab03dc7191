import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import type { INavigationItem, INavigationComposable, INavigationService } from 'src/types/navigation';
import { NavigationService } from 'src/services/navigation/NavigationService';

/**
 * @function useNavigation
 * @description Navigasyon verilerini yöneten composable fonksiyon.
 * Composition API'nin gücünden yararlanarak reaktif navigasyon verisi sağlar.
 * Single Responsibility Prensibi'ne uygun olarak, sadece navigasyon state yönetiminden sorumludur.
 * 
 * @param {string[]} userRoles - Kullanıcının rolleri (opsiyonel)
 * @param {boolean} isAuthenticated - Kullanıcının kimlik doğrulama durumu (opsiyonel)
 * @returns {INavigationComposable} Navigasyon composable arayüzü
 */
export function useNavigation(
  userRoles: string[] = [],
  isAuthenticated: boolean = false
): INavigationComposable {
  
  // Router instance'ını al
  const router = useRouter();
  
  // Navigasyon servisini oluştur
  const navigationService: INavigationService = new NavigationService(router.getRoutes());
  
  // Reaktif state'ler
  const navigationItems = ref<INavigationItem[]>([]);
  const isLoading = ref<boolean>(false);
  const currentUserRoles = ref<string[]>(userRoles);
  const currentAuthStatus = ref<boolean>(isAuthenticated);

  /**
   * @computed visibleNavigationItems
   * @description Kullanıcının görebileceği navigasyon öğelerini hesaplar
   */
  const visibleNavigationItems = computed<INavigationItem[]>(() => {
    return navigationService.getVisibleNavigationItems(
      currentUserRoles.value,
      currentAuthStatus.value
    );
  });

  /**
   * @method loadNavigationItems
   * @description Navigasyon öğelerini yükler
   * @private
   */
  const loadNavigationItems = async (): Promise<void> => {
    try {
      isLoading.value = true;
      
      // Simüle edilmiş async işlem (gerçek uygulamada API çağrısı olabilir)
      await new Promise(resolve => setTimeout(resolve, 100));
      
      navigationItems.value = navigationService.getNavigationItems();
    } catch (error) {
      console.error('Navigasyon öğeleri yüklenirken hata oluştu:', error);
      navigationItems.value = [];
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * @method refreshNavigation
   * @description Navigasyon verilerini yeniden yükler
   * @returns {Promise<void>}
   */
  const refreshNavigation = async (): Promise<void> => {
    await loadNavigationItems();
  };

  /**
   * @method filterByRole
   * @description Navigasyon öğelerini kullanıcı rollerine göre filtreler
   * @param {string[]} roles - Kullanıcı rolleri
   */
  const filterByRole = (roles: string[]): void => {
    currentUserRoles.value = roles;
  };

  /**
   * @method updateAuthStatus
   * @description Kullanıcının kimlik doğrulama durumunu günceller
   * @param {boolean} authStatus - Yeni kimlik doğrulama durumu
   */
  const updateAuthStatus = (authStatus: boolean): void => {
    currentAuthStatus.value = authStatus;
  };

  /**
   * @method getNavigationItemByName
   * @description Belirtilen ada sahip navigasyon öğesini döndürür
   * @param {string} name - Aranacak navigasyon öğesinin adı
   * @returns {INavigationItem | undefined} Bulunan navigasyon öğesi veya undefined
   */
  const getNavigationItemByName = (name: string): INavigationItem | undefined => {
    return navigationService.getNavigationItemByName(name);
  };

  /**
   * @method isNavigationItemActive
   * @description Belirtilen navigasyon öğesinin aktif olup olmadığını kontrol eder
   * @param {string} itemName - Kontrol edilecek öğenin adı
   * @param {string} currentRouteName - Mevcut rotanın adı
   * @returns {boolean} Öğenin aktif olup olmadığı
   */
  const isNavigationItemActive = (itemName: string, currentRouteName: string): boolean => {
    return itemName === currentRouteName;
  };

  // Component mount edildiğinde navigasyon öğelerini yükle
  onMounted(() => {
    loadNavigationItems();
  });

  // INavigationComposable arayüzünü uygula
  return {
    navigationItems: visibleNavigationItems,
    isLoading,
    refreshNavigation,
    filterByRole,
    
    // Ek yardımcı metodlar
    updateAuthStatus,
    getNavigationItemByName,
    isNavigationItemActive,
    
    // Raw navigation items (filtrelenmemiş)
    allNavigationItems: navigationItems
  } as INavigationComposable & {
    updateAuthStatus: (authStatus: boolean) => void;
    getNavigationItemByName: (name: string) => INavigationItem | undefined;
    isNavigationItemActive: (itemName: string, currentRouteName: string) => boolean;
    allNavigationItems: typeof navigationItems;
  };
}

/**
 * @function useNavigationItem
 * @description Tek bir navigasyon öğesi için yardımcı composable
 * @param {INavigationItem} item - Navigasyon öğesi
 * @returns {object} Navigasyon öğesi yardımcıları
 */
export function useNavigationItem(item: INavigationItem) {
  const router = useRouter();

  /**
   * @method navigateToItem
   * @description Belirtilen navigasyon öğesine yönlendirir
   */
  const navigateToItem = async (): Promise<void> => {
    try {
      await router.push({ name: item.name });
    } catch (error) {
      console.error(`${item.name} rotasına yönlendirme hatası:`, error);
    }
  };

  /**
   * @method isExternalLink
   * @description Navigasyon öğesinin harici bir link olup olmadığını kontrol eder
   * @returns {boolean} Harici link olup olmadığı
   */
  const isExternalLink = (): boolean => {
    return item.path.startsWith('http://') || item.path.startsWith('https://');
  };

  return {
    navigateToItem,
    isExternalLink
  };
}
