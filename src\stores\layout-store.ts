import { defineStore } from 'pinia';

/**
 * @interface ILayoutStore
 * @description Layout ile ilgili durumları ve aksiyonları tanımlayan arayüz.
 */
interface ILayoutStore {
    leftDrawerOpen: boolean;
}

/**
 * @function useLayoutStore
 * @description Uygulamanın genel layout durumunu yöneten Pinia store'u.
 * Bu store, çekmecenin (drawer) açık/kapalı durumunu merkezi olarak yönetir.
 * SOLID prensiplerinden Tek Sorumluluk Prensibi'ne (Single Responsibility Principle) uygun olarak,
 * sadece layout ile ilgili durum yönetiminden sorumludur.
 */
export const useLayoutStore = defineStore('layout', {
    state: (): ILayoutStore => ({
        /**
         * @property {boolean} leftDrawerOpen
         * @description Sol çekmecenin açık olup olmadığını belirten durum.
         * Varsayılan olarak kapalıdır.
         */
        leftDrawerOpen: false,
    }),

    actions: {
        /**
         * @method toggleLeftDrawer
         * @description Sol çekmecenin açık/kapalı durumunu tersine çevirir.
         * Bu action, bileşenlerin doğrudan state'i manipüle etmesini engeller
         * ve durum değişimini merkezi bir noktadan kontrol etmeyi sağlar.
         */
        toggleLeftDrawer() {
            this.leftDrawerOpen = !this.leftDrawerOpen;
        },
    },
});