import { defineStore } from 'pinia';
import { ref } from 'vue';
import { useQuasar } from 'quasar';
import type { IThemeStore } from 'src/stores/interfaces/ITheme';
import { Theme } from 'src/stores/interfaces/ITheme';

/**
 * @function useThemeStore
 * @description Uygulamanın tema ayarlarını yöneten Pinia store'u.
 * Bu store, mevcut tema durumunu tutar ve tema değiştirme aksiyonunu sağlar.
 * SOLID prensiplerinden Tek Sorumluluk Prensibi'ne (Single Responsibility Principle) uygun olarak,
 * sadece dil yönetimi ile ilgili durum yönetiminden sorumludur.
 */
export const useThemeStore = defineStore('theme', (): IThemeStore => {
    const $q = useQuasar();

    /**
     * @property {Theme} currentTheme
     * @description Uygulamanın mevcut temasını tutan ref.
     * Başlangıçta localStorage'dan veya sistem tercihinden (Quasar'ın dark modu) alınır.
     */
    const currentTheme = ref<Theme>(
        (localStorage.getItem('appTheme') as Theme) ||
        ($q.dark.isActive ? Theme.DARK : Theme.LIGHT)
    );

    /**
     * @method applyTheme
     * @param {Theme} theme - Uygulanacak tema modu.
     * @description Quasar'ın dark modunu güncelleyerek global temayı uygular.
     * Bu fonksiyon, tema uygulama mantığını kapsüller ve Open/Closed Prensibi'ne uygun olarak
     * tema uygulama mekanizmasının genişletilebilir olmasını sağlar.
     */
    const applyTheme = (theme: Theme) => {
        $q.dark.set(theme === Theme.DARK);
        document.documentElement.setAttribute('data-theme', theme); // CSS değişkenleri için
    };

    /**
     * @method setTheme
     * @param {Theme} theme - Ayarlanacak tema modu.
     * @description Uygulamanın temasını değiştirir ve seçilen temayı localStorage'a kaydeder.
     * Ayrıca `applyTheme` fonksiyonunu çağırarak global temayı günceller.
     */
    const setTheme = (theme: Theme) => {
        currentTheme.value = theme;
        localStorage.setItem('appTheme', theme);
        applyTheme(theme);
    };

    /**
     * @method toggleTheme
     * @description Mevcut temayı tersine çevirir (açık ise koyu, koyu ise açık yapar).
     * Bu aksiyon, kullanıcı arayüzünden gelen basit bir geçiş isteğini yönetir.
     */
    const toggleTheme = () => {
        const newTheme =
            currentTheme.value === Theme.LIGHT ? Theme.DARK : Theme.LIGHT;
        setTheme(newTheme);
    };

    /**
     * @method initializeTheme
     * @description Uygulamanın başlangıcında tema tercihini localStorage'dan yükler ve uygular.
     * Bu metod, tema kalıcılığını ve Quasar'ın dark modunun doğru şekilde ayarlanmasını sağlar.
     * Single Responsibility Prensibi'ne uygun olarak, tema başlatma mantığını kapsüller.
     */
    const initializeTheme = () => {
        const savedTheme = localStorage.getItem('appTheme') as Theme;
        if (savedTheme && Object.values(Theme).includes(savedTheme)) {
            setTheme(savedTheme);
        } else {
            setTheme(currentTheme.value);
        }
    };

    return {
        currentTheme,
        toggleTheme,
        setTheme,
        initializeTheme, // Yeni eklenen initializeTheme fonksiyonu
    };
});