// import type { RouteRecordRaw } from 'vue-router';
import type { Ref } from 'vue';

/**
 * @interface INavigationItem
 * @description Navigasyon menüsünde gösterilecek bir öğeyi temsil eden arayüz.
 * Single Responsibility Prensibi'ne uygun o<PERSON>ak, sadece navigasyon öğesi verilerini tanımlar.
 */
export interface INavigationItem {
  /**
   * @property {string} name
   * @description Rotanın benzersiz adı (route name)
   */
  name: string;

  /**
   * @property {string} path
   * @description Rotanın URL yolu
   */
  path: string;

  /**
   * @property {string} title
   * @description Navigasyon menüsünde görüntülenecek başlık (i18n anahtarı)
   */
  title: string;

  /**
   * @property {string} icon
   * @description Navigasyon öğesinin ikonu (Quasar ikon adı)
   */
  icon: string;

  /**
   * @property {number} order
   * @description Navigasyon menüsündeki sıralama değeri
   */
  order: number;

  /**
   * @property {boolean} requiresAuth
   * @description Bu rotaya erişim için kimlik doğrulama gerekip gerekmediği
   */
  requiresAuth: boolean;

  /**
   * @property {string[]} roles
   * @description Bu rotaya erişim için gerekli roller
   */
  roles: string[];

  /**
   * @property {INavigationItem[]} children
   * @description Alt navigasyon öğeleri (iç içe menüler için)
   */
  children?: INavigationItem[];
}

/**
 * @interface INavigationService
 * @description Navigasyon verilerini yöneten servis arayüzü.
 * Interface Segregation Prensibi'ne uygun olarak, sadece navigasyon ile ilgili işlemleri tanımlar.
 */
export interface INavigationService {
  /**
   * @method getNavigationItems
   * @description Tüm navigasyon öğelerini döndürür
   * @returns {INavigationItem[]} Navigasyon öğeleri listesi
   */
  getNavigationItems(): INavigationItem[];

  /**
   * @method getVisibleNavigationItems
   * @description Kullanıcının görebileceği navigasyon öğelerini döndürür
   * @param {string[]} userRoles - Kullanıcının rolleri
   * @param {boolean} isAuthenticated - Kullanıcının kimlik doğrulama durumu
   * @returns {INavigationItem[]} Görünür navigasyon öğeleri listesi
   */
  getVisibleNavigationItems(userRoles?: string[], isAuthenticated?: boolean): INavigationItem[];

  /**
   * @method getNavigationItemByName
   * @description Belirtilen ada sahip navigasyon öğesini döndürür
   * @param {string} name - Aranacak navigasyon öğesinin adı
   * @returns {INavigationItem | undefined} Bulunan navigasyon öğesi veya undefined
   */
  getNavigationItemByName(name: string): INavigationItem | undefined;
}

/**
 * @interface INavigationComposable
 * @description Navigasyon composable'ının döndüreceği değerleri tanımlayan arayüz.
 * Dependency Inversion Prensibi'ne uygun olarak, üst seviye bileşenlerin bu arayüze bağımlı olmasını sağlar.
 */
export interface INavigationComposable {
  /**
   * @property {Ref<INavigationItem[]>} navigationItems
   * @description Reaktif navigasyon öğeleri listesi
   */
  navigationItems: Ref<INavigationItem[]>;

  /**
   * @property {Ref<boolean>} isLoading
   * @description Navigasyon verilerinin yüklenme durumu
   */
  isLoading: Ref<boolean>;

  /**
   * @method refreshNavigation
   * @description Navigasyon verilerini yeniden yükler
   * @returns {Promise<void>}
   */
  refreshNavigation(): Promise<void>;

  /**
   * @method filterByRole
   * @description Navigasyon öğelerini kullanıcı rollerine göre filtreler
   * @param {string[]} roles - Kullanıcı rolleri
   * @returns {void}
   */
  filterByRole(roles: string[]): void;

  /**
   * @method updateAuthStatus
   * @description Kullanıcının kimlik doğrulama durumunu günceller
   * @param {boolean} authStatus - Yeni kimlik doğrulama durumu
   * @returns {void}
   */
  updateAuthStatus(authStatus: boolean): void;

  /**
   * @method getNavigationItemByName
   * @description Belirtilen ada sahip navigasyon öğesini döndürür
   * @param {string} name - Aranacak navigasyon öğesinin adı
   * @returns {INavigationItem | undefined} Bulunan navigasyon öğesi veya undefined
   */
  getNavigationItemByName(name: string): INavigationItem | undefined;

  /**
   * @method isNavigationItemActive
   * @description Belirtilen navigasyon öğesinin aktif olup olmadığını kontrol eder
   * @param {string} itemName - Kontrol edilecek öğenin adı
   * @param {string} currentRouteName - Mevcut rotanın adı
   * @returns {boolean} Öğenin aktif olup olmadığı
   */
  isNavigationItemActive(itemName: string, currentRouteName: string): boolean;

  /**
   * @property {Ref<INavigationItem[]>} allNavigationItems
   * @description Filtrelenmemiş tüm navigasyon öğeleri
   */
  allNavigationItems: Ref<INavigationItem[]>;
}

/**
 * @type NavigationItemClickHandler
 * @description Navigasyon öğesine tıklandığında çalışacak fonksiyon tipi
 */
export type NavigationItemClickHandler = (item: INavigationItem) => void;

/**
 * @interface INavigationItemProps
 * @description NavigationItem bileşeninin props arayüzü
 */
export interface INavigationItemProps {
  /**
   * @property {INavigationItem} item
   * @description Render edilecek navigasyon öğesi
   */
  item: INavigationItem;

  /**
   * @property {boolean} isActive
   * @description Bu navigasyon öğesinin aktif olup olmadığı
   */
  isActive?: boolean;

  /**
   * @property {NavigationItemClickHandler} onItemClick
   * @description Öğeye tıklandığında çalışacak fonksiyon
   */
  onItemClick?: NavigationItemClickHandler;
}

/**
 * @interface INavigationListProps
 * @description NavigationList bileşeninin props arayüzü
 */
export interface INavigationListProps {
  /**
   * @property {INavigationItem[]} items
   * @description Render edilecek navigasyon öğeleri listesi
   */
  items: INavigationItem[];

  /**
   * @property {string} activeItemName
   * @description Aktif navigasyon öğesinin adı
   */
  activeItemName?: string;

  /**
   * @property {NavigationItemClickHandler} onItemClick
   * @description Öğeye tıklandığında çalışacak fonksiyon
   */
  onItemClick?: NavigationItemClickHandler;
}
