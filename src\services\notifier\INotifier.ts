// src/services/notifier/INotifier.ts

/**
 * @enum NotificationType
 * Bildirim türlerini tanımlar.
 * Başarı, hata, uyarı ve bilgi gibi farklı bildirim senaryoları için kullanılır.
 */
export enum NotificationType {
    Success = 'success',
    Error = 'error',
    Warning = 'warning',
    Info = 'info',
}

/**
 * @interface NotificationAction
 * Bildirimlerdeki isteğe bağlı eylem düğmeleri için arayüz.
 * Bir metin (label) ve tıklandığında çalışacak bir fonksiyon (handler) içerir.
 */
export interface NotificationAction {
    label: string;
    handler: () => void;
}

/**
 * @interface NotificationOptions
 * Bir bildirim için yapılandırma seçeneklerini tanımlar.
 * Mesaj, başlık, süre, konum ve isteğe bağlı eylem düğmeleri gibi parametreleri içerir.
 */
export interface NotificationOptions {
    timeout?: number; // Süre (ms cinsinden)
    position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'top' | 'bottom' | 'left' | 'right' | 'center'; // Konum
    actions?: NotificationAction[]; // İsteğe bağlı eylem düğmeleri
    closeBtn?: boolean; // Kapatma butonu gösterilsin mi?
}

/**
 * @interface INotifier
 * Bildirim servisi için temel arayüz.
 * Farklı türlerde bildirim gösterme metotlarını tanımlar.
 * Bu arayüz, somut bildirim uygulamalarının (örn. QuasarNotifier) uyması gereken sözleşmedir.
 */
export interface INotifier {
    /**
     * Başarı bildirimi gösterir.
     * @param message Bildirim mesajı.
     * @param caption İsteğe bağlı başlık.
     * @param options Diğer bildirim seçenekleri.
     */
    success(message: string, caption?: string, options?: NotificationOptions): void;

    /**
     * Hata bildirimi gösterir.
     * @param message Bildirim mesajı.
     * @param caption İsteğe bağlı başlık.
     * @param options Diğer bildirim seçenekleri.
     */
    error(message: string, caption?: string, options?: NotificationOptions): void;

    /**
     * Uyarı bildirimi gösterir.
     * @param message Bildirim mesajı.
     * @param caption İsteğe bağlı başlık.
     * @param options Diğer bildirim seçenekleri.
     */
    warning(message: string, caption?: string, options?: NotificationOptions): void;

    /**
     * Bilgi bildirimi gösterir.
     * @param message Bildirim mesajı.
     * @param caption İsteğe bağlı başlık.
     * @param options Diğer bildirim seçenekleri.
     */
    info(message: string, caption?: string, options?: NotificationOptions): void;
}