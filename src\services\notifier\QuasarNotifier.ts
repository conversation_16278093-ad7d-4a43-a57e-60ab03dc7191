// src/services/notifier/QuasarNotifier.ts
import { Notify } from 'quasar'; // Notify statik metodunu import et
import type { INotifier, NotificationOptions } from './INotifier';
import { NotificationType } from './INotifier';

/**
 * @class QuasarNotifier
 * INotifier arayüzünü uygulayan somut bildirim sınıfı.
 * Quasar'ın `Notify.create` statik metodunu kullanarak bildirimleri gösterir.
 * Bu sınıf, Quasar'a özgü bildirim mantığını kapsüller ve varsayılan ayarları .env'den alır.
 */
export class QuasarNotifier implements INotifier {
    // .env dosyasından varsayılan değerleri al
    private defaultTimeout: number = Number(import.meta.env.VITE_DEFAULT_NOTIFICATION_TIMEOUT) || 2500;
    private defaultPosition: NotificationOptions['position'] = (import.meta.env.VITE_DEFAULT_NOTIFICATION_POSITION as NotificationOptions['position']) || 'top-right';
    private defaultCloseButton: boolean = (import.meta.env.VITE_DEFAULT_NOTIFICATION_CLOSE_BUTTON === 'true');

    /**
     * Ortak bildirim mantığını işleyen özel metot.
     * @param type Bildirim türü (success, error, warning, info).
     * @param message Bildirim mesajı.
     * @param caption İsteğe bağlı başlık.
     * @param options Diğer bildirim seçenekleri.
     */
    private showNotification(type: NotificationType, message: string, caption?: string, options?: NotificationOptions): void {
        const colorMap: Record<NotificationType, string> = {
            [NotificationType.Success]: 'positive',
            [NotificationType.Error]: 'negative',
            [NotificationType.Warning]: 'warning',
            [NotificationType.Info]: 'info',
        };

        const iconMap: Record<NotificationType, string> = {
            [NotificationType.Success]: 'check_circle',
            [NotificationType.Error]: 'error',
            [NotificationType.Warning]: 'warning',
            [NotificationType.Info]: 'info',
        };

        Notify.create({ // Notify.create statik metodunu kullan
            message: message,
            caption: caption || '',
            timeout: options?.timeout ?? this.defaultTimeout,
            position: options?.position ?? this.defaultPosition as NonNullable<NotificationOptions['position']>,
            color: colorMap[type],
            icon: iconMap[type],
            actions: options?.actions?.map(action => ({
                label: action.label,
                color: 'white',
                handler: action.handler,
            })) || [],
            // Kapatma butonu ayarı
            // Eğer options.closeBtn belirtilmemişse .env'den gelen varsayılanı kullan
            // Eğer options.closeBtn açıkça false ise, kapatma butonu eklenmez.
            // Eğer options.closeBtn true ise veya varsayılan true ise, kapatma butonu eklenir.
            ...(options?.closeBtn ?? this.defaultCloseButton ? {
                actions: [
                    ...(options?.actions || []), // Mevcut eylemleri koru
                    { icon: 'close', color: 'white', handler: () => { /* Kapatma işlemi */ } }
                ]
            } : {})
        });
    }

    /**
     * Başarı bildirimi gösterir.
     * @param message Bildirim mesajı.
     * @param caption İsteğe bağlı başlık.
     * @param options Diğer bildirim seçenekleri.
     */
    success(message: string, caption?: string, options?: NotificationOptions): void {
        this.showNotification(NotificationType.Success, message, caption, options);
    }

    /**
     * Hata bildirimi gösterir.
     * @param message Bildirim mesajı.
     * @param caption İsteğe bağlı başlık.
     * @param options Diğer bildirim seçenekleri.
     */
    error(message: string, caption?: string, options?: NotificationOptions): void {
        this.showNotification(NotificationType.Error, message, caption, options);
    }

    /**
     * Uyarı bildirimi gösterir.
     * @param message Bildirim mesajı.
     * @param caption İsteğe bağlı başlık.
     * @param options Diğer bildirim seçenekleri.
     */
    warning(message: string, caption?: string, options?: NotificationOptions): void {
        this.showNotification(NotificationType.Warning, message, caption, options);
    }

    /**
     * Bilgi bildirimi gösterir.
     * @param message Bildirim mesajı.
     * @param caption İsteğe bağlı başlık.
     * @param options Diğer bildirim seçenekleri.
     */
    info(message: string, caption?: string, options?: NotificationOptions): void {
        this.showNotification(NotificationType.Info, message, caption, options);
    }
}