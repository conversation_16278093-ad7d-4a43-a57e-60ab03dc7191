<template>
  <q-page class="row items-center justify-evenly">
    <q-btn color="positive" label="Başarı Bildirimi" @click="showSuccess" />
    <q-btn color="negative" label="Hata Bildirimi" @click="showError" />
    <q-btn color="warning" label="Uyarı Bildirimi" @click="showWarning" />
    <q-btn color="info" label="Bilgi Bildirimi" @click="showInfo" />
    <q-btn color="primary" label="Eylemli Bildirim" @click="showActionNotification" />
  </q-page>
</template>

<script setup lang="ts">
import { useNotifier } from 'src/services/notifier/useNotifier';

const notifier = useNotifier();

const showSuccess = () => {
  notifier.success('İşlem başarıyla tamamlandı!', 'Veriler güncellendi.');
};

const showError = () => {
  notifier.error('Bir hata oluştu!', 'Lütfen tekrar deneyin.');
};

const showWarning = () => {
  notifier.warning('Dikkat!', 'Bazı bilgiler eksik olabilir.');
};

const showInfo = () => {
  notifier.info('Yeni bir güncelleme mevcut.', 'Daha fazla bilgi için tıklayın.');
};

const showActionNotification = () => {
  notifier.info('Ayarlar kaydedilsin mi?', 'Değişiklikleriniz henüz kaydedilmedi.', {
    timeout: 0, // Süresiz bildirim
    closeBtn: false, // Kapatma butonu gösterilsin
    actions: [
      {
        label: 'Kaydet',
        handler: () => {
          notifier.success('Ayarlar kaydedildi!');
        },
      },
      {
        label: 'İptal',
        handler: () => {
          notifier.warning('Kaydetme işlemi iptal edildi.');
        },
      },
    ],
  });
};
</script>
