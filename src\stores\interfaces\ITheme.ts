/**
 * @enum Theme
 * @description Uygulamanın desteklediği tema modlarını tanımlar.
 * B<PERSON> enum, tema değerleri için tek bir doğruluk kaynağı sağlar ve olası hataları önler.
 * Single Responsibility Prensibi'ne uygun olarak, sadece tema modlarının tanımlanmasından sorumludur.
 */
export enum Theme {
    LIGHT = 'light',
    DARK = 'dark',
}

/**
 * @interface IThemeStore
 * @description Tema store'unun durumunu ve aksiyonlarını tanımlayan arayüz.
 * Interface Segregation Prensibi'ne uygun olarak, store'un dışarıya sunduğu arayüzü netleştirir.
 */
import type { Ref } from 'vue'; // Ref tipini içe aktar

export interface IThemeStore {
    currentTheme: Ref<Theme>;
    toggleTheme(): void;
    setTheme(theme: Theme): void;
    initializeTheme(): void;
}