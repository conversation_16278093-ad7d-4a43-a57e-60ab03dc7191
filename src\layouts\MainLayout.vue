<template>
  <q-layout view="lHh Lpr lFf">
    <!-- AppHeader bileşeni, uygulamanın başlık kısmını temsil eder. -->
    <!-- Başlık metni prop olarak iletilir. -->
    <AppHeader :title="t(String(route.meta.title || 'Quasar App'))" />

    <!-- AppDrawer bileşeni, uygulamanın sol çekmecesini temsil eder. -->
    <!-- Çekmecenin açık/kapalı durumu Pinia store tarafından yönetilir. -->
    <AppDrawer />

    <q-page-container>
      <router-view />
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import AppHeader from 'src/components/layout/AppHeader.vue';
import AppDrawer from 'src/components/layout/AppDrawer.vue';
import { useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';

const route = useRoute();
const { t } = useI18n();
</script>
