// src/services/notifier/useNotifier.ts
import type { INotifier } from './INotifier';
import { QuasarNotifier } from './QuasarNotifier';

// Notifier instance'ını bir kere oluşturup yeniden kullanmak için.
// Bu, uygulamanın yaşam döngüsü boyunca tek bir notifier örneği olmasını sağlar (Singleton benzeri).
const notifierInstance: INotifier = new QuasarNotifier();

/**
 * @function useNotifier
 * Uygulama genelinde bildirim servisine erişim sağlayan bir Vue Composition API composable'ı.
 * Bu composable, INotifier arayüzünü uygulayan somut bir notifier örneğini döndürür.
 * Böylece, bileşenler doğrudan QuasarNotifier'a bağımlı olmaz, arayüze bağımlı olur.
 * Bu, test edilebilirliği ve esnekliği artırır.
 * @returns INotifier Bildirim servisi arayüzü.
 */
export function useNotifier(): INotifier {
    return notifierInstance;
}