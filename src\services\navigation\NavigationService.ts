import type { RouteRecordRaw } from 'vue-router';
import type { INavigationItem, INavigationService } from 'src/types/navigation';

/**
 * @class NavigationService
 * @description Navigasyon verilerini yöneten servis sınıfı.
 * Single Responsibility Prensibi'ne uygun olarak, sadece navigasyon verilerinin işlenmesinden sorumludur.
 * Open/Closed Prensibi'ne uygun olarak, yeni navigasyon türleri için genişletilebilir.
 */
export class NavigationService implements INavigationService {
  private routes: RouteRecordRaw[];

  /**
   * @constructor
   * @param {RouteRecordRaw[]} routes - Vue Router rotaları
   */
  constructor(routes: RouteRecordRaw[]) {
    this.routes = routes;
  }

  /**
   * @method getNavigationItems
   * @description Tüm rotalardan navigasyon öğelerini çıkarır ve döndürür
   * @returns {INavigationItem[]} Navigasyon öğeleri listesi
   */
  getNavigationItems(): INavigationItem[] {
    const navigationItems: INavigationItem[] = [];

    this.extractNavigationItems(this.routes, navigationItems);

    // Sıralama değerine göre sırala
    return navigationItems.sort((a, b) => a.order - b.order);
  }

  /**
   * @method getVisibleNavigationItems
   * @description Kullanıcının görebileceği navigasyon öğelerini döndürür
   * @param {string[]} userRoles - Kullanıcının rolleri
   * @param {boolean} isAuthenticated - Kullanıcının kimlik doğrulama durumu
   * @returns {INavigationItem[]} Görünür navigasyon öğeleri listesi
   */
  getVisibleNavigationItems(userRoles: string[] = [], isAuthenticated: boolean = false): INavigationItem[] {
    const allItems = this.getNavigationItems();

    return allItems.filter(item => this.isItemVisible(item, userRoles, isAuthenticated));
  }

  /**
   * @method getNavigationItemByName
   * @description Belirtilen ada sahip navigasyon öğesini döndürür
   * @param {string} name - Aranacak navigasyon öğesinin adı
   * @returns {INavigationItem | undefined} Bulunan navigasyon öğesi veya undefined
   */
  getNavigationItemByName(name: string): INavigationItem | undefined {
    const allItems = this.getNavigationItems();
    return this.findItemByName(allItems, name);
  }

  /**
   * @private
   * @method extractNavigationItems
   * @description Rotalardan navigasyon öğelerini çıkarır (recursive)
   * @param {RouteRecordRaw[]} routes - İşlenecek rotalar
   * @param {INavigationItem[]} result - Sonuç listesi
   * @param {string} parentPath - Üst rota yolu
   */
  private extractNavigationItems(
    routes: RouteRecordRaw[],
    result: INavigationItem[],
    parentPath: string = ''
  ): void {
    for (const route of routes) {
      // Alt rotaları önce kontrol et (layout rotaları için)
      if (route.children && route.children.length > 0) {
        // Eğer bu rota bir layout rotası ise (meta yok veya showInNav yok), children'ları işle
        if (!route.meta || route.meta.showInNav === undefined) {
          this.extractNavigationItems(route.children, result, this.buildFullPath(parentPath, route.path));
          continue;
        }
      }

      // Meta verisi olmayan rotaları atla
      if (!route.meta) continue;

      // showInNav false olan rotaları atla
      if (route.meta.showInNav === false) continue;

      // Name olmayan rotaları atla
      if (!route.name) continue;

      const fullPath = this.buildFullPath(parentPath, route.path);

      const navigationItem: INavigationItem = {
        name: String(route.name),
        path: fullPath,
        title: route.meta.title || String(route.name),
        icon: route.meta.icon || 'circle',
        order: route.meta.order || 999,
        requiresAuth: route.meta.requiresAuth || false,
        roles: route.meta.roles || [],
        children: []
      };

      // Alt rotaları işle (showInNav true olan rotalar için)
      if (route.children && route.children.length > 0) {
        this.extractNavigationItems(route.children, navigationItem.children!, fullPath);

        // Alt öğesi olmayan parent'ları kaldır (sadece showInNav true olanları tut)
        if (navigationItem.children!.length === 0 && route.meta.showInNav !== true) {
          continue;
        }
      }

      result.push(navigationItem);
    }
  }

  /**
   * @private
   * @method buildFullPath
   * @description Tam rota yolunu oluşturur
   * @param {string} parentPath - Üst rota yolu
   * @param {string} currentPath - Mevcut rota yolu
   * @returns {string} Tam rota yolu
   */
  private buildFullPath(parentPath: string, currentPath: string): string {
    if (!currentPath) return parentPath;
    if (currentPath.startsWith('/')) return currentPath;

    const cleanParentPath = parentPath.endsWith('/') ? parentPath.slice(0, -1) : parentPath;
    const cleanCurrentPath = currentPath.startsWith('/') ? currentPath : `/${currentPath}`;

    return `${cleanParentPath}${cleanCurrentPath}`;
  }

  /**
   * @private
   * @method isItemVisible
   * @description Bir navigasyon öğesinin kullanıcı için görünür olup olmadığını kontrol eder
   * @param {INavigationItem} item - Kontrol edilecek öğe
   * @param {string[]} userRoles - Kullanıcının rolleri
   * @param {boolean} isAuthenticated - Kullanıcının kimlik doğrulama durumu
   * @returns {boolean} Öğenin görünür olup olmadığı
   */
  private isItemVisible(item: INavigationItem, userRoles: string[], isAuthenticated: boolean): boolean {
    // Kimlik doğrulama gerekiyorsa ve kullanıcı giriş yapmamışsa gizle
    if (item.requiresAuth && !isAuthenticated) {
      return false;
    }

    // Rol gerekliliği varsa ve kullanıcının uygun rolü yoksa gizle
    if (item.roles.length > 0 && !item.roles.some(role => userRoles.includes(role))) {
      return false;
    }

    return true;
  }

  /**
   * @private
   * @method findItemByName
   * @description Navigasyon öğeleri listesinde belirtilen ada sahip öğeyi arar (recursive)
   * @param {INavigationItem[]} items - Aranacak öğeler listesi
   * @param {string} name - Aranacak öğenin adı
   * @returns {INavigationItem | undefined} Bulunan öğe veya undefined
   */
  private findItemByName(items: INavigationItem[], name: string): INavigationItem | undefined {
    for (const item of items) {
      if (item.name === name) {
        return item;
      }

      if (item.children && item.children.length > 0) {
        const found = this.findItemByName(item.children, name);
        if (found) {
          return found;
        }
      }
    }

    return undefined;
  }
}
