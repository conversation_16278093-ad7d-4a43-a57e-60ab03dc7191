<template>
  <q-btn flat round dense icon="language">
    <q-menu>
      <q-list style="min-width: 100px">
        <q-item
          v-for="lang in languageOptions"
          :key="lang.value"
          clickable
          v-close-popup
          @click="selectLanguage(lang.value)"
        >
          <q-item-section>
            <q-item-label>{{ lang.label }}</q-item-label>
          </q-item-section>
          <q-item-section side v-if="lang.value === languageStore.currentLanguage">
            <q-icon name="check" />
          </q-item-section>
        </q-item>
      </q-list>
    </q-menu>
  </q-btn>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useLanguageStore } from 'src/stores/language-store';
import { useI18n } from 'vue-i18n';

const languageStore = useLanguageStore();
const { t } = useI18n();

/**
 * @property {Array<{ label: string; value: string }>} languageOptions
 * @description QMenu bileşeninin seçenekleri için <PERSON> computed property.
 * Mevcut dillerin listesini ve bunların kullanıcı arayüzünde gösterilecek etiketlerini içerir.
 */
const languageOptions = computed(() => {
  return languageStore.availableLanguages.map((lang) => ({
    label: getLanguageLabel(lang),
    value: lang,
  }));
});

/**
 * @method getLanguageLabel
 * @param {string} langCode - Dil kodu (örn: 'en-US', 'tr-TR').
 * @returns {string} Dilin kullanıcı arayüzünde gösterilecek etiketi.
 * @description Verilen dil koduna göre dilin okunabilir etiketini döndürür.
 * Bu, i18n çevirileri kullanılarak yapılır.
 */
const getLanguageLabel = (langCode: string): string => {
  switch (langCode) {
    case 'en-US':
      return t('language.english');
    case 'tr-TR':
      return t('language.turkish');
    default:
      return langCode;
  }
};

/**
 * @method selectLanguage
 * @param {string} langCode - Seçilen dil kodu.
 * @description Kullanıcı bir dil seçtiğinde çağrılır ve store'daki dili günceller.
 */
const selectLanguage = (langCode: string) => {
  languageStore.setLanguage(langCode);
};
</script>

<style scoped lang="scss">
/* Bileşene özgü stiller buraya eklenebilir */
</style>
