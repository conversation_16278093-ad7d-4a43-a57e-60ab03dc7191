# Srv-Takip Projesi Do<PERSON>, srv-takip projesinin src klasörü altındaki dosya yapısını, her dosyanın işlevini ve ilişkilerini açıklamaktadır.

## Proje <PERSON>

```
src/
├── App.vue                          # Ana uygulama bileşeni, tüm uygulamanın kök bileşeni
├── assets/                          # Statik varlıklar klasörü
│   └── quasar-logo-vertical.svg     # Quasar logosu
├── boot/                            # Uygulama başlangıcında yüklenen modüller
│   ├── .gitkeep                     # Git için boş klasör işaretleyicisi
│   ├── axios.ts                     # HTTP istekleri için Axios yapılandırması
│   ├── data-source.ts               # Veri kaynağı başlatma ve yapılandırma
│   ├── error-handler.ts             # Hata yakalama ve işleme mekanizması
│   ├── i18n.ts                      # Çoklu dil desteği yapılandırması
│   ├── logger.ts                    # Loglama sistemi ba<PERSON>
│   ├── notifier.ts                  # Bildirim sistemi başlatma
│   ├── theme.ts                     # Tema yönetimi başlatma
│   └── user-module.js               # Kullanıcı modülü başlatma
├── components/                      # Genel kullanım bileşenleri
│   ├── language/                    # Dil ile ilgili bileşenler
│   │   └── LanguageSelector.vue     # Dil seçim bileşeni (layout-store.ts ile çalışır)
│   ├── layout/                      # Düzen bileşenleri
│   │   ├── AppDrawer.vue            # Uygulama çekmecesi (MainLayout.vue ile ilişkili)
│   │   └── AppHeader.vue            # Uygulama başlığı (MainLayout.vue ile ilişkili)
│   └── theme/                       # Tema bileşenleri
│       └── ThemeToggle.vue          # Tema değiştirme bileşeni (theme-store.ts ile çalışır)
├── core/                            # Çekirdek uygulama modülleri
│   ├── datasources/                 # Veri kaynağı uygulamaları
│   │   ├── FirebaseDataSource.ts    # Firebase veri kaynağı uygulaması (IDataSource arayüzünü uygular)
│   │   └── RestApiDataSource.ts     # REST API veri kaynağı uygulaması (IDataSource arayüzünü uygular)
│   ├── di.ts                        # Bağımlılık enjeksiyonu konteyner yapılandırması
│   ├── factory.ts                   # Veri kaynağı fabrikası (Factory Pattern)
│   ├── interfaces/                  # Arayüzler
│   │   ├── IDataSource.ts           # Tüm veri kaynağı işlemlerini birleştiren arayüz
│   │   ├── IReadDataSource.ts       # Veri okuma işlemleri arayüzü
│   │   ├── IRepository.ts           # Repository pattern arayüzü
│   │   ├── IWriteDataSource.ts      # Veri yazma işlemleri arayüzü
│   │   └── BaseModel.ts                # Veri modelleri ve enum tanımları
│   └── repositories/                # Repository uygulamaları
│       └── GenericRepository.ts     # Genel repository uygulaması (IRepository arayüzünü uygular)
├── css/                             # CSS stil dosyaları
│   ├── app.scss                     # Ana uygulama stilleri
│   └── quasar.variables.scss        # Quasar değişkenleri (tema renkleri vb.)
├── env.d.ts                         # TypeScript için ortam tip tanımları
├── i18n/                            # Çoklu dil desteği
│   ├── en-US/                       # İngilizce çeviriler
│   │   └── index.ts                 # İngilizce çeviri tanımları
│   ├── index.ts                     # i18n yapılandırması ve dil yükleme
│   └── tr-TR/                       # Türkçe çeviriler
│       └── index.ts                 # Türkçe çeviri tanımları
├── layouts/                         # Sayfa düzenleri
│   └── MainLayout.vue               # Ana sayfa düzeni (AppHeader ve AppDrawer bileşenlerini kullanır)
├── logging/                         # Loglama sistemi
│   ├── ConsoleLogger.ts             # Konsol loglama uygulaması (ILogger arayüzünü uygular)
│   ├── interfaces/                  # Loglama arayüzleri
│   │   └── ILogger.ts               # Logger arayüzü
│   └── useLogger.ts                 # Logger composable fonksiyonu
├── notification/                    # Bildirim sistemi
│   ├── QuasarNotifier.ts            # Quasar bildirim uygulaması (INotifier arayüzünü uygular)
│   ├── interfaces/                  # Bildirim arayüzleri
│   │   └── INotifier.ts             # Bildirim arayüzü
│   └── useNotifier.ts               # Bildirim composable fonksiyonu
├── pages/                           # Ana uygulama sayfaları
│   ├── ErrorNotFound.vue            # 404 hata sayfası
│   ├── IndexPage.vue                # Ana sayfa (Dashboard) - splitPanel.vue bileşenini kullanır
│   └── TestPage.vue                 # Test sayfası
├── router/                          # Vue Router yapılandırması
│   ├── index.ts                     # Router yapılandırması ve başlatma
│   └── routes.ts                    # Ana uygulama rotaları (modül rotalarını içe aktarır)
└── stores/                          # Pinia durum yönetimi
    ├── index.ts                     # Store yapılandırması ve başlatma
    ├── language-store.ts            # Dil ayarları store'u
    ├── layout-store.ts              # Düzen ayarları store'u
    └── theme-store.ts               # Tema ayarları store'u
```

## Mimari Yapı ve İlişkiler

### Veri Erişim Katmanı

- `core/interfaces/` altındaki arayüzler (IDataSource, IReadDataSource, IWriteDataSource) veri erişim katmanının sözleşmelerini tanımlar
- `core/datasources/` altındaki sınıflar bu arayüzleri uygulayarak farklı veri kaynaklarına erişim sağlar
- `core/factory.ts` Factory Pattern kullanarak uygun veri kaynağını oluşturur
- `core/repositories/GenericRepository.ts` Repository Pattern kullanarak veri işlemlerini soyutlar

### Modüler Yapı

- `modules/` altındaki her klasör bağımsız bir modülü temsil eder
- Her modül kendi bileşenlerini, sayfalarını, servislerini ve store'larını içerir
- `modules/user/index.ts` gibi dosyalar modülün ana uygulamaya nasıl entegre edileceğini tanımlar
- Modüller kendi rotalarını ve çevirilerini içerir, ana uygulamaya dinamik olarak eklenir

### Bağımlılık Enjeksiyonu

- `core/di.ts` bağımlılık enjeksiyonu konteynerini yapılandırır
- Servisler ve veri kaynakları bu konteyner aracılığıyla enjekte edilir
- Bu yapı, bağımlılıkların gevşek bağlanmasını (loose coupling) sağlar

### Composable Fonksiyonlar

- `logging/useLogger.ts`, `notification/useNotifier.ts` gibi composable fonksiyonlar Vue bileşenlerinde kullanılmak üzere işlevsellik sağlar
- `modules/user/composables/useUsers.ts` gibi modüle özgü composable'lar ilgili store'ları ve servisleri kullanarak veri işlemlerini soyutlar

### Tema ve Dil Desteği

- `stores/theme-store.ts` ve `stores/language-store.ts` tema ve dil ayarlarını yönetir
- `components/theme/ThemeToggle.vue` ve `components/language/LanguageSelector.vue` bu store'ları kullanarak kullanıcı arayüzü sağlar
- `i18n/` klasörü çoklu dil desteği için çevirileri içerir

### Hata Yönetimi ve Loglama

- `core/ErrorHandler.ts` merkezi hata yakalama ve işleme mekanizması sağlar
- `logging/` altındaki dosyalar loglama işlevselliği sunar
- `notification/` altındaki dosyalar kullanıcıya bildirim gösterme işlevselliği sağlar

### Uygulama Başlatma

- `boot/` klasöründeki dosyalar uygulama başlangıcında çeşitli servisleri ve modülleri başlatır
- Her boot dosyası belirli bir işlevselliği yapılandırır ve başlatır

Bu proje, SOLID prensiplerine uygun, modüler ve genişletilebilir bir mimari yapıya sahiptir. Interface Segregation, Dependency Inversion ve Single Responsibility prensipleri özellikle veri erişim katmanında belirgindir.
