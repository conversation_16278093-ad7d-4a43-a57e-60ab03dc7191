<template>
  <!-- <PERSON><PERSON><PERSON><PERSON>, Pinia store'da<PERSON> leftDrawer<PERSON>pen durumuna bağlıdır. -->
  <q-drawer
    v-model="layoutStore.leftDrawerOpen"
    show-if-above
    bordered
    :width="drawerWidth"
    :class="drawerClasses"
  >
    <!-- Sabit Header -->
    <div class="drawer-header">
      <div class="drawer-header__content">
        <q-avatar size="40px" class="drawer-header__avatar">
          <q-icon name="apps" size="24px" />
        </q-avatar>
        <div class="drawer-header__info">
          <div class="drawer-header__title">{{ appTitle }}</div>
          <div class="drawer-header__subtitle">{{ appVersion }}</div>
        </div>
      </div>
    </div>

    <!-- Navigation Content -->
    <div class="drawer-body">
      <!-- Debug bilgisi -->
      <div style="padding: 8px; background: #f0f0f0; font-size: 12px">
        DEBUG: Items count = {{ navigationItems.length }}, Loading = {{ isNavigationLoading }}
      </div>

      <!-- Test: Basit liste -->
      <div style="padding: 8px">
        <div style="font-weight: bold; margin-bottom: 8px">Test Navigation:</div>
        <div
          v-for="item in navigationItems"
          :key="item.name"
          style="padding: 4px; border: 1px solid #ccc; margin: 2px 0"
        >
          {{ item.name }} - {{ item.title }}
        </div>
      </div>

      <div class="drawer-content">
        <!-- Ana Navigasyon -->
        <NavigationList
          :items="navigationItems"
          :active-item-name="currentRouteName"
          :is-loading="isNavigationLoading"
          :show-auth-indicators="true"
          :show-separator="true"
          title="navigation.main"
          @item-click="handleNavigationClick"
        />

        <!-- Kullanıcı Menüsü (eğer giriş yapmışsa) -->
        <NavigationList
          v-if="isAuthenticated && userNavigationItems.length > 0"
          :items="userNavigationItems"
          :active-item-name="currentRouteName"
          :show-auth-indicators="false"
          :show-separator="true"
          title="navigation.user"
          @item-click="handleNavigationClick"
        />

        <!-- Admin Menüsü (eğer admin ise) -->
        <NavigationList
          v-if="isAdmin && adminNavigationItems.length > 0"
          :items="adminNavigationItems"
          :active-item-name="currentRouteName"
          :show-auth-indicators="false"
          title="navigation.admin"
          @item-click="handleNavigationClick"
        />
      </div>
    </div>

    <!-- Sabit Footer -->
    <div class="drawer-footer">
      <q-btn
        flat
        dense
        icon="settings"
        :label="t('navigation.settings')"
        class="drawer-footer__settings-btn"
        @click="openSettings"
      />
    </div>
  </q-drawer>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useLayoutStore } from 'src/stores/layout-store';
import { useNavigation } from 'src/composables/navigation/useNavigation';
import type { INavigationItem } from 'src/types/navigation';
import NavigationList from 'src/components/navigation/NavigationList.vue';

// Composable'lar
const layoutStore = useLayoutStore();
const route = useRoute();
const { t } = useI18n();

// Navigasyon composable'ı - şimdilik basit kullanıcı bilgileri
const navigation = useNavigation([], false); // Başlangıçta giriş yapmamış kullanıcı
const navigationItems = navigation.allNavigationItems; // Filtrelenmemiş tüm öğeler
const isNavigationLoading = navigation.isLoading;

// Uygulama bilgileri
const appTitle = ref<string>('AIST App');
const appVersion = ref<string>('v1.0.0');

// Drawer ayarları
const drawerWidth = ref<number>(280);

// Kullanıcı durumu (şimdilik mock data - gerçek uygulamada auth store'dan gelecek)
const isAuthenticated = ref<boolean>(false);
const userRoles = ref<string[]>([]);

// Computed properties
const currentRouteName = computed<string>(() => {
  return String(route.name || '');
});

const drawerClasses = computed<string>(() => {
  const classes = ['app-drawer'];

  if (isAuthenticated.value) {
    classes.push('app-drawer--authenticated');
  }

  return classes.join(' ');
});

const isAdmin = computed<boolean>(() => {
  return userRoles.value.includes('admin');
});

// Kullanıcı ve admin navigasyon öğeleri (şimdilik boş - gelecekte genişletilebilir)
const userNavigationItems = computed<INavigationItem[]>(() => {
  // Kullanıcıya özel navigasyon öğeleri
  return [];
});

const adminNavigationItems = computed<INavigationItem[]>(() => {
  // Admin'e özel navigasyon öğeleri
  return [];
});

// Methods
const handleNavigationClick = (item: INavigationItem): void => {
  console.log('Navigasyon öğesine tıklandı:', item);

  // Mobil cihazlarda drawer'ı kapat
  if (window.innerWidth < 1024) {
    layoutStore.leftDrawerOpen = false;
  }
};

const openSettings = (): void => {
  console.log('Ayarlar açılıyor...');
  // Ayarlar sayfasına yönlendirme veya modal açma
};

// Kullanıcı durumu değiştiğinde navigasyonu güncelle
const updateUserStatus = (authenticated: boolean, roles: string[] = []): void => {
  isAuthenticated.value = authenticated;
  userRoles.value = roles;
  navigation.updateAuthStatus(authenticated);
  navigation.filterByRole(roles);
};

// Export for external use (if needed)
defineExpose({
  updateUserStatus,
});
</script>

<style scoped lang="scss">
.app-drawer {
  display: flex;
  flex-direction: column;
  height: 100vh;

  // Sabit Header
  .drawer-header {
    background: linear-gradient(135deg, var(--q-primary) 0%, var(--q-secondary) 100%);
    color: white;
    flex-shrink: 0; // Header'ın küçülmesini engelle
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    &__content {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
    }

    &__avatar {
      background: rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.3);
    }

    &__info {
      flex: 1;
      min-width: 0;
    }

    &__title {
      font-size: 1.1rem;
      font-weight: 600;
      line-height: 1.2;
      margin-bottom: 2px;
    }

    &__subtitle {
      font-size: 0.8rem;
      opacity: 0.8;
      line-height: 1;
    }
  }

  // Scrollable Body - Flex ile genişleyen alan
  .drawer-body {
    flex: 1; // Kalan alanı kapla
    min-height: 0; // Flex child için gerekli
    overflow: hidden;
  }

  .drawer-scroll {
    height: 100%; // Parent'ın tüm yüksekliğini kullan
  }

  .drawer-content {
    padding: 8px 0;
  }

  // Sabit Footer
  .drawer-footer {
    background: var(--q-card-background);
    border-top: 1px solid var(--q-separator-color);
    flex-shrink: 0; // Footer'ın küçülmesini engelle
    padding: 12px 16px;

    &__settings-btn {
      width: 100%;
      justify-content: flex-start;
      color: var(--q-text-color);

      &:hover {
        background-color: rgba(var(--q-primary-rgb), 0.1);
      }
    }
  }

  // Authenticated state
  &--authenticated {
    .drawer-header {
      background: linear-gradient(135deg, var(--q-positive) 0%, var(--q-primary) 100%);
    }
  }
}

// Dark mode adjustments
.body--dark {
  .app-drawer {
    .drawer-footer {
      border-top-color: rgba(255, 255, 255, 0.1);
    }
  }
}

// Responsive adjustments
@media (max-width: 1023px) {
  .app-drawer {
    .drawer-header {
      &__title {
        font-size: 1rem;
      }

      &__subtitle {
        font-size: 0.75rem;
      }
    }
  }
}

// Animation improvements
.app-drawer {
  .drawer-header,
  .drawer-content,
  .drawer-footer {
    transition: all 0.3s ease-in-out;
  }
}
</style>
