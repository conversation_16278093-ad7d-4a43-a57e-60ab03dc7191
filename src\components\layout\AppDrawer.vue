<template>
  <!-- <PERSON><PERSON><PERSON><PERSON>, Pinia store'da<PERSON> leftDrawerOpen durumuna bağlıdır. -->
  <q-drawer v-model="layoutStore.leftDrawerOpen" show-if-above bordered>
    <!-- Çekmece içeriği buraya eklenebilir. -->
    <!-- <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> öğeleri veya kullanıcı bilgileri. -->
    <q-list>
      <q-item-label header> Essential Links </q-item-label>

      <!-- <PERSON><PERSON><PERSON> bir link -->
      <q-item clickable tag="a" target="_blank" href="https://quasar.dev">
        <q-item-section avatar>
          <q-icon name="school" />
        </q-item-section>
        <q-item-section>
          <q-item-label>Docs</q-item-label>
          <q-item-label caption>quasar.dev</q-item-label>
        </q-item-section>
      </q-item>
    </q-list>
  </q-drawer>
</template>

<script setup lang="ts">
import { useLayoutStore } from 'src/stores/layout-store';

// Pinia layout store'u kullanılır.
const layoutStore = useLayoutStore();
</script>

<style scoped lang="scss">
/* Bileşene özgü stiller buraya eklenebilir */
</style>
