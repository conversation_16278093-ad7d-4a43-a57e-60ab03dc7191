import { defineStore } from 'pinia';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import messages from 'src/i18n';

/**
 * @interface ILanguageStore
 * @description Dil store'unun durumunu tanımlayan arayüz.
 */

/**
 * @function useLanguageStore
 * @description Uygulamanın dil ayarlarını yöneten Pinia store'u.
 * Bu store, mevcut dilin durumunu tutar ve dil değiştirme aksiyonunu sağlar.
 * SOLID prensiplerinden Tek Sorumluluk Prensibi'ne (Single Responsibility Principle) uygun olarak,
 * sadece dil yönetimi ile ilgili durum yönetiminden sorumludur.
 */
export const useLanguageStore = defineStore('language', () => {
    const i18n = useI18n();

    /**
     * @property {string} currentLanguage
     * @description Uygulamanın mevcut dilini tutan ref.
     * Başlangıçta localStorage'dan veya varsayılan olarak 'en-US' olarak ayarlanır.
     */
    const currentLanguage = ref<string>(localStorage.getItem('appLanguage') || i18n.locale.value || 'en-US');

    /**
     * @property {string[]} availableLanguages
     * @description Uygulamada mevcut olan dillerin listesi.
     * src/i18n/index.ts dosyasından dinamik olarak alınır.
     */
    const availableLanguages = Object.keys(messages);

    /**
     * @method setLanguage
     * @param {string} lang - Ayarlanacak dil kodu (örn: 'en-US', 'tr-TR').
     * @description Uygulamanın dilini değiştirir ve seçilen dili localStorage'a kaydeder.
     * Bu aksiyon, i18n instance'ının locale değerini güncelleyerek tüm uygulamada dilin değişmesini sağlar.
     */
    const setLanguage = (lang: string) => {
        if (availableLanguages.includes(lang)) {
            i18n.locale.value = lang;
            currentLanguage.value = lang;
            localStorage.setItem('appLanguage', lang);
        } else {
            console.warn(`Desteklenmeyen dil kodu: ${lang}`);
        }
    };

    // Uygulama yüklendiğinde localStorage'daki dili ayarla
    if (currentLanguage.value !== i18n.locale.value) {
        setLanguage(currentLanguage.value);
    }

    return {
        currentLanguage,
        availableLanguages,
        setLanguage,
    };
});