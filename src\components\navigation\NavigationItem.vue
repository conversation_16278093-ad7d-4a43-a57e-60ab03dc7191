<template>
  <q-item :class="navigationItemClasses" clickable v-ripple @click="handleItemClick">
    <!-- <PERSON><PERSON> bölü<PERSON>ü -->
    <q-item-section avatar>
      <q-icon :name="item.icon" :class="iconClasses" />
    </q-item-section>

    <!-- <PERSON><PERSON><PERSON><PERSON> bölümü -->
    <q-item-section>
      <q-item-label :class="labelClasses">
        {{ translatedTitle }}
      </q-item-label>

      <!-- Alt başlık varsa göster -->
      <q-item-label v-if="item.path && showPath" caption :class="captionClasses">
        {{ item.path }}
      </q-item-label>
    </q-item-section>

    <!-- Sa<PERSON> taraf gö<PERSON> -->
    <q-item-section side v-if="hasRightIndicators">
      <!-- <PERSON><PERSON> doğrulama gerekli göstergesi -->
      <q-icon
        v-if="item.requiresAuth && showAuthIndicator"
        name="lock"
        size="xs"
        :class="authIndicatorClasses"
      />

      <!-- Alt öğe varsa ok göstergesi -->
      <q-icon v-if="hasChildren" name="chevron_right" size="xs" :class="chevronClasses" />
    </q-item-section>
  </q-item>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';
import type { INavigationItem, NavigationItemClickHandler } from 'src/types/navigation';
import { useNavigationItem } from 'src/composables/navigation/useNavigation';

/**
 * @interface INavigationItemProps
 * @description NavigationItem bileşeninin props arayüzü
 */
interface INavigationItemProps {
  /**
   * @property {INavigationItem} item
   * @description Render edilecek navigasyon öğesi
   */
  item: INavigationItem;

  /**
   * @property {boolean} isActive
   * @description Bu navigasyon öğesinin aktif olup olmadığı
   */
  isActive?: boolean | undefined;

  /**
   * @property {boolean} showPath
   * @description Rota yolunun gösterilip gösterilmeyeceği
   */
  showPath?: boolean | undefined;

  /**
   * @property {boolean} showAuthIndicator
   * @description Kimlik doğrulama göstergesinin gösterilip gösterilmeyeceği
   */
  showAuthIndicator?: boolean | undefined;

  /**
   * @property {NavigationItemClickHandler} onItemClick
   * @description Öğeye tıklandığında çalışacak fonksiyon
   */
  onItemClick?: NavigationItemClickHandler | undefined;
}

// Props tanımlaması
const props = defineProps<INavigationItemProps>();

// Emits tanımlaması
const emit = defineEmits<{
  itemClick: [item: INavigationItem];
}>();

// Composable'lar
const { t } = useI18n();
const route = useRoute();
const { navigateToItem, isExternalLink } = useNavigationItem(props.item);

// Default değerler
const isActive = computed(() => props.isActive ?? false);
const showPath = computed(() => props.showPath ?? false);
const showAuthIndicator = computed(() => props.showAuthIndicator ?? true);

/**
 * @computed translatedTitle
 * @description i18n ile çevrilmiş başlık
 */
const translatedTitle = computed<string>(() => {
  return t(props.item.title);
});

/**
 * @computed hasChildren
 * @description Alt öğe olup olmadığını kontrol eder
 */
const hasChildren = computed<boolean>(() => {
  return Boolean(props.item.children && props.item.children.length > 0);
});

/**
 * @computed hasRightIndicators
 * @description Sağ tarafta gösterilecek gösterge olup olmadığını kontrol eder
 */
const hasRightIndicators = computed<boolean>(() => {
  return (props.item.requiresAuth && showAuthIndicator.value) || hasChildren.value;
});

/**
 * @computed isCurrentlyActive
 * @description Öğenin şu anda aktif olup olmadığını hesaplar
 */
const isCurrentlyActive = computed<boolean>(() => {
  return isActive.value || route.name === props.item.name;
});

/**
 * @computed navigationItemClasses
 * @description Navigasyon öğesi için CSS sınıfları
 */
const navigationItemClasses = computed<string>(() => {
  const classes = ['navigation-item'];

  if (isCurrentlyActive.value) {
    classes.push('navigation-item--active');
  }

  if (props.item.requiresAuth) {
    classes.push('navigation-item--auth-required');
  }

  if (hasChildren.value) {
    classes.push('navigation-item--has-children');
  }

  return classes.join(' ');
});

/**
 * @computed iconClasses
 * @description İkon için CSS sınıfları
 */
const iconClasses = computed<string>(() => {
  const classes = ['navigation-item__icon'];

  if (isCurrentlyActive.value) {
    classes.push('navigation-item__icon--active');
  }

  return classes.join(' ');
});

/**
 * @computed labelClasses
 * @description Etiket için CSS sınıfları
 */
const labelClasses = computed<string>(() => {
  const classes = ['navigation-item__label'];

  if (isCurrentlyActive.value) {
    classes.push('navigation-item__label--active');
  }

  return classes.join(' ');
});

/**
 * @computed captionClasses
 * @description Alt başlık için CSS sınıfları
 */
const captionClasses = computed<string>(() => {
  return 'navigation-item__caption';
});

/**
 * @computed authIndicatorClasses
 * @description Kimlik doğrulama göstergesi için CSS sınıfları
 */
const authIndicatorClasses = computed<string>(() => {
  return 'navigation-item__auth-indicator text-orange';
});

/**
 * @computed chevronClasses
 * @description Chevron ikonu için CSS sınıfları
 */
const chevronClasses = computed<string>(() => {
  return 'navigation-item__chevron text-grey-6';
});

/**
 * @method handleItemClick
 * @description Öğeye tıklandığında çalışan fonksiyon
 */
const handleItemClick = async (): Promise<void> => {
  // Önce custom click handler'ı çalıştır
  if (props.onItemClick) {
    props.onItemClick(props.item);
  }

  // Event emit et
  emit('itemClick', props.item);

  // Harici link değilse ve alt öğe yoksa navigasyon yap
  if (!isExternalLink() && !hasChildren.value) {
    await navigateToItem();
  }

  // Harici linkse yeni sekmede aç
  if (isExternalLink()) {
    window.open(props.item.path, '_blank');
  }
};
</script>

<style scoped lang="scss">
.navigation-item {
  border-radius: 8px;
  margin: 2px 8px;
  transition: all 0.2s ease-in-out;

  &:hover {
    background-color: rgba(var(--q-primary-rgb), 0.1);
  }

  &--active {
    background-color: rgba(var(--q-primary-rgb), 0.15);
    border-left: 3px solid var(--q-primary);
  }

  &--auth-required {
    position: relative;
  }

  &--has-children {
    .navigation-item__label {
      font-weight: 500;
    }
  }

  &__icon {
    transition: color 0.2s ease-in-out;

    &--active {
      color: var(--q-primary);
    }
  }

  &__label {
    transition: color 0.2s ease-in-out;
    font-weight: 400;

    &--active {
      color: var(--q-primary);
      font-weight: 500;
    }
  }

  &__caption {
    font-size: 0.75rem;
    opacity: 0.7;
  }

  &__auth-indicator {
    opacity: 0.8;
  }

  &__chevron {
    opacity: 0.6;
  }
}

// Dark mode desteği
.body--dark {
  .navigation-item {
    &:hover {
      background-color: rgba(var(--q-primary-rgb), 0.2);
    }

    &--active {
      background-color: rgba(var(--q-primary-rgb), 0.25);
    }
  }
}
</style>
