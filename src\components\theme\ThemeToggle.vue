<template>
  <q-toggle
    :model-value="isDarkTheme"
    checked-icon="dark_mode"
    unchecked-icon="light_mode"
    color="grey-9"
    size="lg"
    @update:model-value="onToggleChange"
    aria-label="Tema değiştir"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useThemeStore } from 'src/stores/theme-store';
import { Theme } from 'src/stores/interfaces/ITheme';

/**
 * @description Tema değiştirme bileşeni.
 * Kullanıcının açık ve koyu tema arasında geçiş yapmasını sağlar.
 * theme-store.ts ile entegre olarak tema durumunu yönetir ve kalıcılığını sağlar.
 * Single Responsibility Prensibi'ne uygun olarak, sadece tema geçiş UI'sından sorumludur.
 * Smart/Dumb Bileşen Ayrımı prensibine göre, bu bileşen tema durumunu doğrudan store'dan alır ve günceller.
 */

const themeStore = useThemeStore();

/**
 * @property {boolean} isDarkTheme
 * @description Mevcut tema durumunu temsil eden computed property.
 * QToggle bileşeninin v-model'i ile iki yönlü bağlıdır.
 */
const isDarkTheme = computed<boolean>(() => themeStore.currentTheme === Theme.DARK);

/**
 * @method onToggleChange
 * @param {boolean} value - QToggle'ın yeni değeri (true: koyu, false: açık).
 * @description QToggle bileşeninin değeri değiştiğinde çağrılan olay işleyici.
 * themeStore'daki toggleTheme aksiyonunu çağırarak tema değişikliğini tetikler.
 */
const onToggleChange = () => {
  // 'value' parametresi kullanılmadığı için kaldırıldı
  themeStore.toggleTheme();
};
</script>

<style lang="scss" scoped>
/* Bileşene özel stil tanımlamaları */
/* Örneğin, toggle'ın rengini veya boyutunu özelleştirebilirsiniz */
</style>
