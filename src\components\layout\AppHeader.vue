<template>
  <q-header elevated>
    <q-toolbar>
      <!-- <PERSON><PERSON> but<PERSON>una tıklandığında Pinia store'daki çekmece durumunu değiştiren action çağrılır. -->
      <q-btn flat dense icon="menu" aria-label="Menu" @click="layoutStore.toggleLeftDrawer()" />

      <!-- Başlık prop olarak alınır ve burada gösterilir. -->
      <q-toolbar-title>{{ title }}</q-toolbar-title>

      <!-- Dil seçim bileşeni -->
      <LanguageSelector />
      <!-- Tema değiştirme bileşeni -->
      <ThemeToggle />
    </q-toolbar>
  </q-header>
</template>

<script setup lang="ts">
import { useLayoutStore } from 'src/stores/layout-store';
import LanguageSelector from 'src/components/language/LanguageSelector.vue';
import ThemeToggle from 'src/components/theme/ThemeToggle.vue'; // ThemeToggle bileşenini içe aktar

/**
 * @interface IAppHeaderProps
 * @description AppHeader bileşeninin alacağı prop'ları tanımlar.
 */
interface IAppHeaderProps {
  title: string; // Başlık metni
}

// Bileşenin alacağı prop'lar tanımlanır.
defineProps<IAppHeaderProps>();

// Pinia layout store'u kullanılır.
const layoutStore = useLayoutStore();
</script>

<style scoped lang="scss">
/* Bileşene özgü stiller buraya eklenebilir */
</style>
