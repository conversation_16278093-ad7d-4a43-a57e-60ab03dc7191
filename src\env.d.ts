declare namespace NodeJS {
  interface ProcessEnv {
    NODE_ENV: string;
    VUE_ROUTER_MODE: 'hash' | 'history' | 'abstract' | undefined;
    VUE_ROUTER_BASE: string | undefined;
  }
}

/**
 * @module vue-router
 * @description Vue Router'ın RouteMeta arayüzünü genişleterek özel meta veri özelliklerini ekler.
 * Bu, rotalara dinamik navigasyon menüsü oluşturma gibi ek bilgiler eklememizi sağlar.
 */
import 'vue-router'; // vue-router'ın global tiplerini genişletmek için

declare module 'vue-router' {
  interface RouteMeta {
    /**
     * @property {string} title
     * @description Navigasyon menüsünde veya sayfa başlığında görüntülenecek başlık.
     * Opsiyoneldir, çünkü her rotanın bir başlığı olmayabilir.
     */
    title?: string; // i18n anahtarı olarak kullanılacak
    showInNav?: boolean;
    icon?: string; // Quasar ikon adı (örneğin: 'home', 'settings')
    [key: string]: unknown;
  }
}
