declare namespace NodeJS {
  interface ProcessEnv {
    NODE_ENV: string;
    VUE_ROUTER_MODE: 'hash' | 'history' | 'abstract' | undefined;
    VUE_ROUTER_BASE: string | undefined;
  }
}

/**
 * @module vue-router
 * @description Vue Router'ın RouteMeta arayüzünü genişleterek özel meta veri özelliklerini ekler.
 * Bu, rotalara dinamik navigasyon menüsü oluşturma gibi ek bilgiler eklememizi sağlar.
 */
import 'vue-router'; // vue-router'ın global tiplerini genişletmek için

declare module 'vue-router' {
  interface RouteMeta {
    /**
     * @property {string} title
     * @description Navigasyon menüsünde veya sayfa başlığında görüntülenecek başlık.
     * i18n anahtarı olarak kullanılacak
     */
    title?: string;

    /**
     * @property {boolean} showInNav
     * @description Bu rotanın navigasyon menüsünde gösterilip gösterilmeyeceğini belirler.
     */
    showInNav?: boolean;

    /**
     * @property {string} icon
     * @description Navigasyon menüsünde gösterilecek Quasar ikon adı (örneğin: 'home', 'settings')
     */
    icon?: string;

    /**
     * @property {number} order
     * @description Navigasyon menüsündeki sıralama için kullanılır. Küçük sayılar önce gelir.
     */
    order?: number;

    /**
     * @property {boolean} requiresAuth
     * @description Bu rotaya erişim için kimlik doğrulama gerekip gerekmediğini belirler.
     */
    requiresAuth?: boolean;

    /**
     * @property {string[]} roles
     * @description Bu rotaya erişim için gerekli roller listesi.
     */
    roles?: string[];

    [key: string]: unknown;
  }
}
