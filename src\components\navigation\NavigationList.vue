<template>
  <q-list class="navigation-list">
    <!-- <PERSON><PERSON><PERSON><PERSON><PERSON> varsa göster -->
    <q-item-label v-if="title" header :class="headerClasses">
      {{ translatedTitle }}
    </q-item-label>

    <!-- Yükleme durumu -->
    <div v-if="isLoading" class="navigation-list__loading">
      <q-item>
        <q-item-section avatar>
          <q-skeleton type="QAvatar" />
        </q-item-section>
        <q-item-section>
          <q-skeleton type="text" width="60%" />
          <q-skeleton type="text" width="40%" />
        </q-item-section>
      </q-item>
    </div>

    <!-- Navigasyon öğeleri -->
    <template v-else>
      <!-- Öğe yoksa boş durum mesajı -->
      <div v-if="items.length === 0" class="navigation-list__empty">
        <q-item>
          <q-item-section avatar>
            <q-icon name="info" color="grey-6" />
          </q-item-section>
          <q-item-section>
            <q-item-label class="text-grey-6">
              {{ emptyMessage }}
            </q-item-label>
          </q-item-section>
        </q-item>
      </div>

      <!-- Navigasyon öğeleri listesi -->
      <template v-else>
        <template v-for="item in sortedItems" :key="item.name">
          <!-- Alt öğesi olmayan normal öğeler -->
          <NavigationItem
            v-if="!item.children || item.children.length === 0"
            :item="item"
            :is-active="isItemActive(item)"
            :show-path="showPaths"
            :show-auth-indicator="showAuthIndicators"
            @item-click="handleItemClick"
          />

          <!-- Alt öğesi olan öğeler için expansion item -->
          <q-expansion-item
            v-else
            :key="`expansion-${item.name}`"
            :class="expansionItemClasses(item)"
            :icon="item.icon"
            :label="getTranslatedTitle(item.title)"
            :default-opened="isItemActive(item) || hasActiveChild(item)"
            :header-class="expansionHeaderClasses(item)"
          >
            <!-- Alt öğeler için recursive NavigationList -->
            <NavigationList
              :items="item.children"
              :active-item-name="activeItemName"
              :show-paths="showPaths"
              :show-auth-indicators="showAuthIndicators"
              :is-loading="false"
              class="navigation-list--nested"
              @item-click="handleItemClick"
            />
          </q-expansion-item>
        </template>
      </template>
    </template>

    <!-- Ayırıcı çizgi -->
    <q-separator v-if="showSeparator" class="navigation-list__separator" />
  </q-list>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import type { INavigationItem, NavigationItemClickHandler } from 'src/types/navigation';
import NavigationItem from './NavigationItem.vue';

/**
 * @interface INavigationListProps
 * @description NavigationList bileşeninin props arayüzü
 */
interface INavigationListProps {
  /**
   * @property {INavigationItem[]} items
   * @description Render edilecek navigasyon öğeleri listesi
   */
  items: INavigationItem[];

  /**
   * @property {string} activeItemName
   * @description Aktif navigasyon öğesinin adı
   */
  activeItemName?: string | undefined;

  /**
   * @property {string} title
   * @description Liste başlığı
   */
  title?: string | undefined;

  /**
   * @property {boolean} showPaths
   * @description Rota yollarının gösterilip gösterilmeyeceği
   */
  showPaths?: boolean | undefined;

  /**
   * @property {boolean} showAuthIndicators
   * @description Kimlik doğrulama göstergelerinin gösterilip gösterilmeyeceği
   */
  showAuthIndicators?: boolean | undefined;

  /**
   * @property {boolean} showSeparator
   * @description Liste sonunda ayırıcı çizgi gösterilip gösterilmeyeceği
   */
  showSeparator?: boolean | undefined;

  /**
   * @property {boolean} isLoading
   * @description Yükleme durumu
   */
  isLoading?: boolean | undefined;

  /**
   * @property {string} emptyMessage
   * @description Boş durum mesajı
   */
  emptyMessage?: string | undefined;

  /**
   * @property {NavigationItemClickHandler} onItemClick
   * @description Öğeye tıklandığında çalışacak fonksiyon
   */
  onItemClick?: NavigationItemClickHandler | undefined;
}

// Props tanımlaması
const props = defineProps<INavigationListProps>();

// Emits tanımlaması
const emit = defineEmits<{
  itemClick: [item: INavigationItem];
}>();

// Composable'lar
const { t } = useI18n();

// Default değerler
const activeItemName = computed(() => props.activeItemName ?? '');
const title = computed(() => props.title ?? '');
const showPaths = computed(() => props.showPaths ?? false);
const showAuthIndicators = computed(() => props.showAuthIndicators ?? true);
const showSeparator = computed(() => props.showSeparator ?? false);
const isLoading = computed(() => props.isLoading ?? false);
const emptyMessage = computed(() => props.emptyMessage ?? 'Navigasyon öğesi bulunamadı');

/**
 * @computed translatedTitle
 * @description i18n ile çevrilmiş başlık
 */
const translatedTitle = computed<string>(() => {
  return title.value ? t(title.value) : '';
});

/**
 * @computed sortedItems
 * @description Sıralanmış navigasyon öğeleri
 */
const sortedItems = computed<INavigationItem[]>(() => {
  return [...props.items].sort((a, b) => a.order - b.order);
});

/**
 * @computed headerClasses
 * @description Başlık için CSS sınıfları
 */
const headerClasses = computed<string>(() => {
  return 'navigation-list__header text-weight-medium';
});

/**
 * @method getTranslatedTitle
 * @description Verilen başlığı çevirir
 * @param {string} title - Çevrilecek başlık
 * @returns {string} Çevrilmiş başlık
 */
const getTranslatedTitle = (title: string): string => {
  return t(title);
};

/**
 * @method isItemActive
 * @description Bir öğenin aktif olup olmadığını kontrol eder
 * @param {INavigationItem} item - Kontrol edilecek öğe
 * @returns {boolean} Öğenin aktif olup olmadığı
 */
const isItemActive = (item: INavigationItem): boolean => {
  return activeItemName.value === item.name;
};

/**
 * @method hasActiveChild
 * @description Bir öğenin aktif alt öğesi olup olmadığını kontrol eder
 * @param {INavigationItem} item - Kontrol edilecek öğe
 * @returns {boolean} Aktif alt öğe olup olmadığı
 */
const hasActiveChild = (item: INavigationItem): boolean => {
  if (!item.children || item.children.length === 0) {
    return false;
  }

  return item.children.some((child) => isItemActive(child) || hasActiveChild(child));
};

/**
 * @method expansionItemClasses
 * @description Expansion item için CSS sınıfları
 * @param {INavigationItem} item - Öğe
 * @returns {string} CSS sınıfları
 */
const expansionItemClasses = (item: INavigationItem): string => {
  const classes = ['navigation-list__expansion'];

  if (isItemActive(item) || hasActiveChild(item)) {
    classes.push('navigation-list__expansion--active');
  }

  if (item.requiresAuth) {
    classes.push('navigation-list__expansion--auth-required');
  }

  return classes.join(' ');
};

/**
 * @method expansionHeaderClasses
 * @description Expansion header için CSS sınıfları
 * @param {INavigationItem} item - Öğe
 * @returns {string} CSS sınıfları
 */
const expansionHeaderClasses = (item: INavigationItem): string => {
  const classes = ['navigation-list__expansion-header'];

  if (isItemActive(item) || hasActiveChild(item)) {
    classes.push('navigation-list__expansion-header--active');
  }

  return classes.join(' ');
};

/**
 * @method handleItemClick
 * @description Öğeye tıklandığında çalışan fonksiyon
 * @param {INavigationItem} item - Tıklanan öğe
 */
const handleItemClick = (item: INavigationItem): void => {
  // Önce custom click handler'ı çalıştır
  if (props.onItemClick) {
    props.onItemClick(item);
  }

  // Event emit et
  emit('itemClick', item);
};
</script>

<style scoped lang="scss">
.navigation-list {
  &__header {
    color: var(--q-primary);
    font-size: 0.875rem;
    padding: 8px 16px 4px 16px;
  }

  &__loading {
    padding: 8px;
  }

  &__empty {
    opacity: 0.7;
  }

  &__separator {
    margin: 8px 16px;
    opacity: 0.3;
  }

  &__expansion {
    margin: 2px 8px;
    border-radius: 8px;

    &--active {
      background-color: rgba(var(--q-primary-rgb), 0.1);
    }

    &--auth-required {
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 8px;
        right: 8px;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: var(--q-orange);
      }
    }
  }

  &__expansion-header {
    padding: 8px 16px;
    border-radius: 8px;
    transition: all 0.2s ease-in-out;

    &--active {
      color: var(--q-primary);
      font-weight: 500;
    }
  }

  &--nested {
    padding-left: 16px;
    border-left: 2px solid rgba(var(--q-primary-rgb), 0.2);
    margin-left: 16px;
  }
}

// Dark mode desteği
.body--dark {
  .navigation-list {
    &__expansion {
      &--active {
        background-color: rgba(var(--q-primary-rgb), 0.2);
      }
    }

    &--nested {
      border-left-color: rgba(var(--q-primary-rgb), 0.3);
    }
  }
}
</style>
