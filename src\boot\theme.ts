import { boot } from 'quasar/wrappers';
import { useThemeStore } from 'src/stores/theme-store';

/**
 * @function themeBoot
 * @description Uygulama başlangıcında tema ayarlarını yükleyen ve uygulayan boot dosyası.
 * Bu boot dosyası, uygulamanın ilk yüklenmesinde tema kalıcılığını sağlar.
 * Single Responsibility Prensibi'ne uygun olarak, sadece tema başlatma mantığından sorumludur.
 */
export default boot(() => {
    const themeStore = useThemeStore();
    themeStore.initializeTheme(); // Tema başlatma mantığını store'dan çağır
});